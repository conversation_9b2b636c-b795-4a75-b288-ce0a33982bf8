import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp, CheckCircle } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Test component to verify auto-display PDF functionality
const AutoPdfTestComponent = () => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null);

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Test case studies specifically for auto-display functionality
  const autoPdfTestCases: CaseStudy[] = [
    {
      id: "auto_pdf_1",
      title: "Auto-Display PDF Test - W3C Document",
      isNew: true,
      likes: 100,
      category: "Auto PDF Testing",
      company: "W3C",
      market: "B2B",
      objective: ["Auto Display", "PDF", "Testing"],
      description: "This case study should automatically display the PDF document alongside the case study details when the modal opens. No user interaction required!",
      image: "https://www.w3.org/Icons/w3c_home",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
    {
      id: "auto_pdf_2",
      title: "Auto-Display Test - Mozilla TraceMonkey Paper",
      isNew: true,
      likes: 95,
      category: "Auto PDF Testing",
      company: "Mozilla",
      market: "B2B",
      objective: ["Auto Display", "Research", "JavaScript"],
      description: "This case study tests auto-display with a larger, more complex PDF document. The PDF should load automatically in the right panel (desktop) or below details (mobile).",
      image: "https://www.mozilla.org/media/img/logos/firefox/logo-quantum.9c5e96634f92.png",
      pdfUrl: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    },
    {
      id: "auto_pdf_3",
      title: "No PDF Test - Should Show Details Only",
      isNew: false,
      likes: 50,
      category: "No PDF Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["No PDF", "Single Column", "Testing"],
      description: "This case study has no PDF URL, so it should display only the case study details in a single column layout. No PDF viewer should appear.",
      image: "https://placehold.co/600x400?text=No+PDF",
      pdfUrl: "",
    },
  ];

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {/* Instructions */}
      <Alert className="mb-6">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Auto-Display PDF Testing</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>Click on any case study card below to test the auto-display PDF functionality:</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>With PDF:</strong> Should automatically show PDF alongside details in split view</li>
              <li><strong>Without PDF:</strong> Should show only case study details in single column</li>
              <li><strong>No Toggle Buttons:</strong> No "Show PDF" or "Hide PDF" buttons should appear</li>
              <li><strong>Responsive:</strong> Split view on desktop, stacked on mobile</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {autoPdfTestCases.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleCaseStudyClick(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src = "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4 text-sm">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span> {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl ? (
                    <Badge variant="default" className="flex items-center gap-1 bg-green-600">
                      <FileText className="h-3 w-3" />
                      Auto PDF
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      No PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500 mt-2">
                <span className="font-medium">Company:</span> {caseStudy.company}
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default AutoPdfTestComponent;
