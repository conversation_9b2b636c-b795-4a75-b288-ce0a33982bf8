import React, { useState, useEffect } from "react";
import axios from "axios";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, AlertCircle, ThumbsUp } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";

// Direct Baserow API integration without using the service
const DirectBaserowCaseStudies = () => {
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(
    null
  );

  // NocoDB API configuration
  const nocodbConfig = {
    apiUrl: "https://app.nocodb.com/api/v2",
    corsProxyUrl: "https://corsproxy.io/?",
    tableId: "mj1s93pidxngxnf",
    viewId: "vwiex09hf5rmqe6g",
    apiToken: "HrLL5CJ07sSRGCLeCqxexsj1dWhyRxqYXElVK5jD",
  };

  // Function to parse objectives from string to array
  const parseObjectives = (objectivesStr: any): string[] => {
    if (!objectivesStr) return [];

    // If it's already an array, ensure all items are strings
    if (Array.isArray(objectivesStr)) {
      return objectivesStr.map((obj) => {
        // Handle case where obj might be an object with id, value, color
        if (obj && typeof obj === "object" && "value" in obj) {
          return obj.value.toString().trim();
        }
        return obj.toString().trim();
      });
    }

    // If it's a JSON string, try to parse it
    if (
      typeof objectivesStr === "string" &&
      (objectivesStr.startsWith("[") || objectivesStr.startsWith("{"))
    ) {
      try {
        const parsed = JSON.parse(objectivesStr);
        if (Array.isArray(parsed)) {
          return parsed.map((obj) => {
            // Handle case where obj might be an object with id, value, color
            if (obj && typeof obj === "object" && "value" in obj) {
              return obj.value.toString().trim();
            }
            return obj.toString().trim();
          });
        }
      } catch (e) {
        console.warn("Failed to parse JSON objectives:", e);
      }
    }

    // If it's a comma-separated string, split it
    if (typeof objectivesStr === "string") {
      return objectivesStr.split(",").map((obj) => obj.trim());
    }

    // If objectivesStr is an object with id, value, color
    if (
      objectivesStr &&
      typeof objectivesStr === "object" &&
      "value" in objectivesStr
    ) {
      return [objectivesStr.value.toString().trim()];
    }

    // If all else fails, convert to string and return as single item array
    return [objectivesStr.toString()];
  };

  // Function to map NocoDB data to our CaseStudy type
  const mapNocodbToCaseStudy = (item: any): CaseStudy => {
    console.log("Mapping NocoDB item to CaseStudy:", item);

    return {
      id: item.id?.toString() || Math.random().toString(36).substring(7),
      title: item.Title || item.Name || "", // Try both Title and Name fields
      isNew: item.IsNew === "true" || item.IsNew === true || false,
      likes: parseInt(item.Likes || "0") || 0, // Parse likes from Baserow
      category: item.Category || "", // This will be used as the name
      company: item.Company || "",
      creator: item.Creator || undefined,
      market: (item.Market as "B2C" | "B2B" | "B2C & B2B") || "B2C",
      objective: parseObjectives(item.Objective || ""),
      description: item.Description || "",
      image:
        item.Logo || item.Image || "https://placehold.co/600x400?text=No+Logo", // Try both Logo and Image fields
      pdfUrl: (item.PDF || item.PdfUrl || "").toString(), // Ensure it's always a string
    };
  };

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Fetch case studies directly from Baserow
  useEffect(() => {
    const fetchCaseStudies = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching case studies directly from NocoDB with config:", {
          apiUrl: nocodbConfig.apiUrl,
          tableId: nocodbConfig.tableId,
          viewId: nocodbConfig.viewId,
          apiToken: nocodbConfig.apiToken.substring(0, 5) + "...", // Only log part of the token for security
        });

        // Try with CORS proxy
        const baseUrl = `${nocodbConfig.apiUrl}/tables/${nocodbConfig.tableId}/records?offset=0&limit=25&where=&viewId=${nocodbConfig.viewId}`;
        const proxyUrl = `${nocodbConfig.corsProxyUrl}${encodeURIComponent(
          baseUrl
        )}`;
        console.log("Direct Request URL (with CORS proxy):", proxyUrl);

        const response = await axios.get(proxyUrl, {
          headers: {
            "Content-Type": "application/json",
            "xc-token": nocodbConfig.apiToken,
          },
        });

        console.log("Direct NocoDB API response:", response.data);

        // NocoDB typically returns data in a 'list' or 'records' array
        let records = [];
        if (response.data && response.data.list) {
          records = response.data.list;
        } else if (response.data && response.data.records) {
          records = response.data.records;
        } else if (Array.isArray(response.data)) {
          records = response.data;
        } else {
          console.warn("No results found in NocoDB response");
          setCaseStudies([]);
          return;
        }

        const mappedData = records.map(mapNocodbToCaseStudy);
        console.log("Mapped case studies:", mappedData);
        setCaseStudies(mappedData);
      } catch (error: any) {
        console.error(
          "Error fetching case studies directly from NocoDB:",
          error
        );

        // More detailed error logging
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error("Response error data:", error.response.data);
          console.error("Response error status:", error.response.status);
          console.error("Response error headers:", error.response.headers);
          setErrorMessage(
            `API Error: ${error.response.status} - ${JSON.stringify(
              error.response.data || {}
            )}`
          );
        } else if (error.request) {
          // The request was made but no response was received
          console.error("Request error:", error.request);
          setErrorMessage(
            "No response received from API. This could be a network issue or CORS problem."
          );
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error("Error message:", error.message);
          setErrorMessage(`Error: ${error.message}`);
        }

        setIsError(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCaseStudies();
  }, []);

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {isLoading ? (
        // Loading state
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <Card
                key={index}
                className="bg-white rounded-md shadow-md overflow-hidden"
              >
                <Skeleton className="w-full h-48" />
                <CardContent className="p-6">
                  <Skeleton className="h-6 w-3/4 mb-3" />
                  <div className="flex items-center space-x-2 mb-3">
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-4" />
                  <div className="flex justify-between items-center mb-2">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-4 w-1/4" />
                  </div>
                  <Skeleton className="h-4 w-1/2 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            ))}
        </div>
      ) : isError ? (
        // Error state
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load case studies from NocoDB directly.
            <p className="text-sm mt-2">{errorMessage}</p>
          </AlertDescription>
        </Alert>
      ) : null}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {caseStudies.length === 0 && !isLoading ? (
          <div className="text-center text-gray-500 col-span-2">
            No case studies found.
          </div>
        ) : (
          caseStudies.map((caseStudy) => (
            <Card
              key={caseStudy.id}
              className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleCaseStudyClick(caseStudy)}
            >
              <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
                <img
                  src={caseStudy.image}
                  alt={`${caseStudy.title} logo`}
                  className="max-h-full max-w-full object-contain"
                  onError={(e) => {
                    e.currentTarget.src =
                      "https://placehold.co/600x400?text=No+Logo";
                  }}
                />
              </div>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-semibold text-stare-navy">
                    {caseStudy.title}
                  </h3>
                  {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
                </div>
                <div className="flex flex-wrap items-center gap-2 mb-3">
                  {caseStudy.objective.map((objective) => {
                    // Handle case where objective might be an object with id, value, color
                    const objectiveText =
                      typeof objective === "object" &&
                      objective !== null &&
                      "value" in objective
                        ? objective.value.toString()
                        : objective.toString();

                    return (
                      <Badge key={objectiveText} variant="outline">
                        {objectiveText}
                      </Badge>
                    );
                  })}
                </div>
                <p className="text-gray-600 mb-4">{caseStudy.description}</p>
                <div className="flex justify-between items-center">
                  <div className="text-gray-500">
                    <span className="font-medium">Name:</span>{" "}
                    {caseStudy.category}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <ThumbsUp className="h-3 w-3" />
                      {caseStudy.likes}
                    </Badge>
                    {caseStudy.pdfUrl && (
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1"
                      >
                        <FileText className="h-3 w-3" />
                        PDF
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-gray-500">
                  <span className="font-medium">Company:</span>{" "}
                  {caseStudy.company}
                </div>
                {caseStudy.creator && (
                  <div className="text-gray-500">
                    <span className="font-medium">Creator:</span>{" "}
                    {caseStudy.creator}
                  </div>
                )}
                <div className="text-gray-500">
                  <span className="font-medium">Market:</span>{" "}
                  {caseStudy.market}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </>
  );
};

export default DirectBaserowCaseStudies;
