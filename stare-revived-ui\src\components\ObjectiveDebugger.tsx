import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle, Info } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getObjectiveText } from "@/utils/objectiveUtils";

// Debug component to identify objective rendering issues
const ObjectiveDebugger = () => {
  const [debugInfo, setDebugInfo] = useState<any[]>([]);

  // Test different objective formats that might cause issues
  const testObjectives = [
    // String objectives (should work)
    ["Testing", "String", "Objectives"],
    
    // Mixed format (potential issue)
    [
      "String Objective",
      { id: 1, value: "Object Objective", color: "#blue" },
      "Another String"
    ],
    
    // All objects (potential issue)
    [
      { id: 1, value: "First Object", color: "#red" },
      { id: 2, value: "Second Object", color: "#green" },
      { id: 3, value: "Third Object", color: "#blue" }
    ],
    
    // Objects without proper structure (potential issue)
    [
      { id: 1, name: "Wrong Structure" }, // Missing 'value' property
      { value: "Correct Structure" },
      { id: 2, value: "Another Correct", color: "#purple" }
    ]
  ];

  useEffect(() => {
    // Process each test case and capture debug info
    const processedInfo = testObjectives.map((objectives, index) => {
      const results = objectives.map((obj, objIndex) => {
        try {
          const text = getObjectiveText(obj as any);
          return {
            original: obj,
            processed: text,
            type: typeof obj,
            hasValue: obj && typeof obj === 'object' && 'value' in obj,
            error: null
          };
        } catch (error) {
          return {
            original: obj,
            processed: null,
            type: typeof obj,
            hasValue: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });
      
      return {
        testCase: index + 1,
        objectives,
        results
      };
    });
    
    setDebugInfo(processedInfo);
  }, []);

  const renderObjective = (obj: any, index: number, testCase: number) => {
    try {
      // This is where the error might occur
      const objectiveText = getObjectiveText(obj);
      
      return (
        <Badge key={`${testCase}-${index}`} variant="outline" className="mr-2 mb-2">
          {objectiveText}
        </Badge>
      );
    } catch (error) {
      return (
        <Badge key={`${testCase}-${index}`} variant="destructive" className="mr-2 mb-2">
          ERROR: {error instanceof Error ? error.message : 'Unknown'}
        </Badge>
      );
    }
  };

  const renderDirectObjective = (obj: any, index: number, testCase: number) => {
    try {
      // This is the problematic pattern that causes the React error
      return (
        <Badge key={`${testCase}-${index}-direct`} variant="outline" className="mr-2 mb-2">
          {obj} {/* This will cause error if obj is an object */}
        </Badge>
      );
    } catch (error) {
      return (
        <Badge key={`${testCase}-${index}-direct`} variant="destructive" className="mr-2 mb-2">
          DIRECT ERROR: {error instanceof Error ? error.message : 'Unknown'}
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Objective Rendering Debugger</AlertTitle>
        <AlertDescription>
          This component helps identify where the "Objects are not valid as a React child" error is coming from.
          It tests different objective formats and shows which ones cause issues.
        </AlertDescription>
      </Alert>

      {debugInfo.map((testInfo, testIndex) => (
        <Card key={testIndex} className="border">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              Test Case {testInfo.testCase}: {
                testInfo.objectives.every((obj: any) => typeof obj === 'string') ? 'All Strings' :
                testInfo.objectives.every((obj: any) => typeof obj === 'object') ? 'All Objects' :
                'Mixed Format'
              }
            </h3>
            
            {/* Debug Information */}
            <div className="mb-4 p-3 bg-gray-50 rounded">
              <h4 className="font-medium mb-2">Debug Info:</h4>
              <pre className="text-xs overflow-x-auto">
                {JSON.stringify(testInfo.objectives, null, 2)}
              </pre>
            </div>

            {/* Safe Rendering (using utility function) */}
            <div className="mb-4">
              <h4 className="font-medium mb-2 text-green-700">✅ Safe Rendering (with getObjectiveText):</h4>
              <div className="flex flex-wrap">
                {testInfo.objectives.map((obj: any, index: number) => 
                  renderObjective(obj, index, testInfo.testCase)
                )}
              </div>
            </div>

            {/* Unsafe Rendering (direct rendering) */}
            <div className="mb-4">
              <h4 className="font-medium mb-2 text-red-700">❌ Unsafe Rendering (direct {'{obj}'}):</h4>
              <div className="flex flex-wrap">
                {testInfo.objectives.map((obj: any, index: number) => {
                  // Only render strings directly to avoid React errors
                  if (typeof obj === 'string') {
                    return (
                      <Badge key={`${testInfo.testCase}-${index}-safe`} variant="outline" className="mr-2 mb-2">
                        {obj}
                      </Badge>
                    );
                  } else {
                    return (
                      <Badge key={`${testInfo.testCase}-${index}-unsafe`} variant="destructive" className="mr-2 mb-2">
                        WOULD CAUSE ERROR: {typeof obj} object
                      </Badge>
                    );
                  }
                })}
              </div>
            </div>

            {/* Processing Results */}
            <div>
              <h4 className="font-medium mb-2">Processing Results:</h4>
              <div className="space-y-2">
                {testInfo.results.map((result: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    {result.error ? (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    <span className="font-mono">
                      {typeof result.original === 'object' 
                        ? JSON.stringify(result.original) 
                        : result.original
                      }
                    </span>
                    <span className="text-gray-500">→</span>
                    <span className="font-mono text-blue-600">
                      {result.processed || result.error}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Summary */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Key Finding</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>The React error occurs when we try to render an object directly in JSX like this:</p>
            <code className="block bg-red-100 p-2 rounded text-sm">
              {`<Badge>{objective}</Badge> // ❌ Error if objective is {id, value, color}`}
            </code>
            <p>Instead, we must extract the text value first:</p>
            <code className="block bg-green-100 p-2 rounded text-sm">
              {`<Badge>{getObjectiveText(objective)}</Badge> // ✅ Safe`}
            </code>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default ObjectiveDebugger;
