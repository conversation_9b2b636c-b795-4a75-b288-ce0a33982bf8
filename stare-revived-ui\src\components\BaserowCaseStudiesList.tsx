import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, AlertCircle, ThumbsUp } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { fetchCaseStudies } from "@/services/nocodbApi";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CaseStudy } from "@/types/caseStudy";
import { caseStudiesData } from "@/data/caseStudiesData";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";

// Using the PdfViewerModal component instead of a custom modal

// Case studies list with Baserow API integration (Legacy)
const BaserowCaseStudiesList = () => {
  // State for detail modal
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(
    null
  );

  // Fetch case studies from Baserow API (Legacy - now using NocoDB service)
  const {
    data: baserowCaseStudies,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["baserow-case-studies"],
    queryFn: fetchCaseStudies,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    onSuccess: (data) => {
      console.log(
        "Successfully fetched case studies for Baserow component:",
        data
      );
    },
    onError: (err) => {
      console.error("Error fetching case studies for Baserow component:", err);
    },
  });

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Use baserowCaseStudies if available, otherwise fall back to caseStudiesData
  const caseStudies = baserowCaseStudies || caseStudiesData;

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {isLoading ? (
        // Loading state
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <Card
                key={index}
                className="bg-white rounded-md shadow-md overflow-hidden"
              >
                <Skeleton className="w-full h-48" />
                <CardContent className="p-6">
                  <Skeleton className="h-6 w-3/4 mb-3" />
                  <div className="flex items-center space-x-2 mb-3">
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-4" />
                  <div className="flex justify-between items-center mb-2">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-4 w-1/4" />
                  </div>
                  <Skeleton className="h-4 w-1/2 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            ))}
        </div>
      ) : isError ? (
        // Error state
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load case studies from Baserow (Legacy). Using fallback
            data.
            {error instanceof Error && (
              <p className="text-sm mt-2">{error.message}</p>
            )}
          </AlertDescription>
        </Alert>
      ) : null}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {caseStudies.length === 0 ? (
          <div className="text-center text-gray-500 col-span-2">
            No case studies found.
          </div>
        ) : (
          caseStudies.map((caseStudy) => (
            <Card
              key={caseStudy.id}
              className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleCaseStudyClick(caseStudy)}
            >
              <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
                <img
                  src={caseStudy.image}
                  alt={`${caseStudy.title} logo`}
                  className="max-h-full max-w-full object-contain"
                  onError={(e) => {
                    e.currentTarget.src =
                      "https://placehold.co/600x400?text=No+Logo";
                  }}
                />
              </div>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-semibold text-stare-navy">
                    {caseStudy.title}
                  </h3>
                  {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
                </div>
                <div className="flex flex-wrap items-center gap-2 mb-3">
                  {caseStudy.objective.map((objective) => {
                    // Handle case where objective might be an object with id, value, color
                    const objectiveText =
                      typeof objective === "object" &&
                      objective !== null &&
                      "value" in objective
                        ? objective.value.toString()
                        : objective.toString();

                    return (
                      <Badge key={objectiveText} variant="outline">
                        {objectiveText}
                      </Badge>
                    );
                  })}
                </div>
                <p className="text-gray-600 mb-4">{caseStudy.description}</p>
                <div className="flex justify-between items-center">
                  <div className="text-gray-500">
                    <span className="font-medium">Name:</span>{" "}
                    {caseStudy.category}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <ThumbsUp className="h-3 w-3" />
                      {caseStudy.likes}
                    </Badge>
                    {caseStudy.pdfUrl && (
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1"
                      >
                        <FileText className="h-3 w-3" />
                        PDF
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-gray-500">
                  <span className="font-medium">Company:</span>{" "}
                  {caseStudy.company}
                </div>
                {caseStudy.creator && (
                  <div className="text-gray-500">
                    <span className="font-medium">Creator:</span>{" "}
                    {caseStudy.creator}
                  </div>
                )}
                <div className="text-gray-500">
                  <span className="font-medium">Market:</span>{" "}
                  {caseStudy.market}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </>
  );
};

export default BaserowCaseStudiesList;
