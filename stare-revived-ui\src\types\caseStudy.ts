export interface CaseStudy {
  id: string;
  title: string;
  isNew: boolean;
  likes: number; // Keeping for backward compatibility
  category: string; // This will be used as the name
  company: string;
  creator?: string;
  market: "B2C" | "B2B" | "B2C & B2B";
  objective: string[]; // Changed from CaseStudyObjective[] to string[] to allow custom values
  description: string;
  image: string; // This will be used for the logo
  pdfUrl?: string; // URL to the PDF file
}

// These are the predefined objectives, but we now allow custom values as well
export type CaseStudyObjective =
  | "Acquisition"
  | "Activation"
  | "Adoption"
  | "Conversion"
  | "Engagement"
  | "First Time Experience"
  | "Gamification"
  | "Growth"
  | "GTM"
  | "Monetization"
  | "MVP"
  | "Notification"
  | "Onboarding"
  | "Personalization"
  | "Retention"
  | "Integration"
  | "API"
  | "Development"
  | "UI/UX"
  | "Security"
  | "Data"
  | "Management"; // Added more objectives to match our mock data

export interface CaseStudiesFilters {
  categories: string[];
  companies: string[];
  markets: ("B2C" | "B2B" | "B2C & B2B")[];
  likesRange: "All" | "More than 100" | "Between 50 to 100" | "Less than 50";
  objectives: CaseStudyObjective[];
  searchQuery: string;
}
