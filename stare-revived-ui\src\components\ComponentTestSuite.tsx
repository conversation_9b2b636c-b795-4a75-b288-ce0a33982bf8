import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp, CheckCircle, AlertTriangle } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Comprehensive test component to verify all fixes
const ComponentTestSuite = () => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null);

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Test case studies with various scenarios
  const testCaseStudies: CaseStudy[] = [
    {
      id: "test_logo_1",
      title: "Logo Display Test - W3C",
      isNew: true,
      likes: 150,
      category: "Logo Testing",
      company: "W3C",
      creator: "Test Creator",
      market: "B2B",
      objective: ["Logo", "Display", "Testing"],
      description: "This case study tests proper logo display with a valid W3C logo URL. The logo should be properly sized and centered in the card.",
      image: "https://www.w3.org/Icons/w3c_home",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
    {
      id: "test_logo_2",
      title: "Logo Display Test - Mozilla",
      isNew: true,
      likes: 125,
      category: "Logo Testing",
      company: "Mozilla",
      creator: "Test Creator",
      market: "B2B",
      objective: ["Logo", "Display", "Testing"],
      description: "This case study tests logo display with Mozilla's Firefox logo. Should maintain aspect ratio and proper positioning.",
      image: "https://www.mozilla.org/media/img/logos/firefox/logo-quantum.9c5e96634f92.png",
      pdfUrl: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    },
    {
      id: "test_broken_logo",
      title: "Broken Logo Test - Fallback",
      isNew: false,
      likes: 75,
      category: "Error Testing",
      company: "Test Corp",
      creator: "Test Creator",
      market: "B2C",
      objective: ["Error Handling", "Fallback", "Testing"],
      description: "This case study tests the fallback behavior when a logo URL is broken. Should show 'No+Logo' placeholder.",
      image: "https://invalid-url-that-does-not-exist.com/broken-logo.png",
      pdfUrl: "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf",
    },
    {
      id: "test_pdf_auto_display",
      title: "Auto PDF Display Test",
      isNew: true,
      likes: 200,
      category: "PDF Auto Display",
      company: "Adobe",
      creator: "Test Creator",
      market: "B2C & B2B",
      objective: ["PDF", "Auto Display", "Testing"],
      description: "This case study tests the auto-display PDF functionality. When opened, the PDF should automatically appear alongside the case study details without requiring any user interaction.",
      image: "https://www.adobe.com/content/dam/cc/icons/Adobe_Corporate_Horizontal_Red_HEX.svg",
      pdfUrl: "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf",
    },
    {
      id: "test_no_pdf",
      title: "No PDF Test - Single Column",
      isNew: false,
      likes: 50,
      category: "Layout Testing",
      company: "Test Corp",
      creator: "Test Creator",
      market: "B2C",
      objective: ["Layout", "No PDF", "Testing"],
      description: "This case study has no PDF URL, so it should display only the case study details in a single column layout. No PDF viewer should appear.",
      image: "https://placehold.co/600x400?text=Test+Corp",
      pdfUrl: "",
    },
    {
      id: "test_responsive_layout",
      title: "Responsive Layout Test",
      isNew: true,
      likes: 180,
      category: "Responsive Testing",
      company: "Responsive Corp",
      creator: "Test Creator",
      market: "B2C & B2B",
      objective: ["Responsive", "Layout", "Mobile", "Desktop"],
      description: "This case study tests responsive layout behavior. On desktop, it should show split view (details + PDF). On mobile, it should stack vertically (details above PDF).",
      image: "https://placehold.co/600x400?text=Responsive+Test",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
  ];

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {/* Test Instructions */}
      <Alert className="mb-6">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Component Test Suite</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>This test suite verifies all the fixes for logo display, PDF functionality, and layout issues:</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>Logo Display:</strong> Test valid logos, broken URLs, and fallback behavior</li>
              <li><strong>PDF Auto-Display:</strong> Verify PDFs appear automatically without toggle buttons</li>
              <li><strong>Responsive Layout:</strong> Check split view (desktop) and stacked view (mobile)</li>
              <li><strong>Error Handling:</strong> Test broken images and invalid PDF URLs</li>
              <li><strong>Consistent Behavior:</strong> Ensure all components work the same way</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Test Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">Logo Display</h4>
                <p className="text-sm text-green-700">Fixed image sizing and fallback</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">PDF Auto-Display</h4>
                <p className="text-sm text-green-700">Removed toggle buttons</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">Responsive Layout</h4>
                <p className="text-sm text-green-700">Split view and mobile stacking</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Cases */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleCaseStudyClick(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src = "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4 text-sm">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span> {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl ? (
                    <Badge variant="default" className="flex items-center gap-1 bg-blue-600">
                      <FileText className="h-3 w-3" />
                      Auto PDF
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      No PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500 mt-2">
                <span className="font-medium">Company:</span> {caseStudy.company}
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default ComponentTestSuite;
