import { baserowClient } from '@/services/baserowApi';
import { baserowConfig } from '@/config/api.config';

/**
 * Tests the Baserow API connection by making a simple request
 * This can be used to verify your API token is working correctly
 */
export const testBaserowConnection = async (): Promise<{
  success: boolean;
  message: string;
  data?: any;
}> => {
  try {
    // Make a simple request to test the connection
    const response = await baserowClient.get(
      `/database/rows/table/${baserowConfig.tableId}/?user_field_names=true&limit=1`
    );
    
    return {
      success: true,
      message: 'Successfully connected to Baserow API',
      data: response.data
    };
  } catch (error: any) {
    // Handle different types of errors
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      if (error.response.status === 401) {
        return {
          success: false,
          message: 'Authentication failed: Invalid API token. Please check your Baserow API token.',
        };
      } else {
        return {
          success: false,
          message: `API error: ${error.response.status} - ${error.response.data?.error || 'Unknown error'}`,
        };
      }
    } else if (error.request) {
      // The request was made but no response was received
      return {
        success: false,
        message: 'No response from Baserow API. Please check your network connection.',
      };
    } else {
      // Something happened in setting up the request that triggered an Error
      return {
        success: false,
        message: `Error: ${error.message}`,
      };
    }
  }
};
