import React from "react";
import { Badge } from "@/components/ui/badge";
import { getObjectiveText } from "@/utils/objectiveUtils";

interface SafeObjectiveRendererProps {
  objectives: any[];
  variant?: "default" | "secondary" | "destructive" | "outline";
  className?: string;
}

/**
 * Safe objective renderer that handles all possible objective formats
 * and prevents React rendering errors
 */
const SafeObjectiveRenderer: React.FC<SafeObjectiveRendererProps> = ({
  objectives,
  variant = "outline",
  className = "",
}) => {
  if (!objectives || !Array.isArray(objectives)) {
    console.warn("SafeObjectiveRenderer: objectives is not an array:", objectives);
    return null;
  }

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {objectives.map((objective, index) => {
        try {
          // Use the utility function to safely extract text
          const objectiveText = getObjectiveText(objective);
          
          // Ensure we have a valid string
          if (!objectiveText || typeof objectiveText !== 'string') {
            console.warn(`SafeObjectiveRenderer: Invalid objective text at index ${index}:`, objective);
            return (
              <Badge key={`error-${index}`} variant="destructive" className="text-xs">
                Invalid Objective
              </Badge>
            );
          }

          return (
            <Badge key={`${objectiveText}-${index}`} variant={variant}>
              {objectiveText}
            </Badge>
          );
        } catch (error) {
          console.error(`SafeObjectiveRenderer: Error rendering objective at index ${index}:`, error, objective);
          return (
            <Badge key={`error-${index}`} variant="destructive" className="text-xs">
              Render Error
            </Badge>
          );
        }
      })}
    </div>
  );
};

export default SafeObjectiveRenderer;
