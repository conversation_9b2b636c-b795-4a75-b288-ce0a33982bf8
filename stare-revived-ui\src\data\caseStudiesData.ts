
import { CaseStudy } from '@/types/caseStudy';

export const caseStudiesData: CaseStudy[] = [
  {
    id: '1',
    title: 'Writing a review on Glassdoor',
    isNew: true,
    likes: 20,
    category: 'User Experience',
    company: 'Glassdoor',
    market: 'B2C',
    objective: ['Engagement'],
    description: 'Analysis of the user flow to write reviews on Glassdoor',
    image: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=500&auto=format&fit=crop'
  },
  {
    id: '2',
    title: 'Reducing Ride Cancellations in popular ride-hailing services like Ola',
    isNew: true,
    likes: 47,
    category: 'User Retention',
    company: 'Ola',
    market: 'B2C',
    objective: ['Conversion', 'Engagement'],
    description: 'Strategies to reduce ride cancellations and improve user experience',
    image: 'https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?w=500&auto=format&fit=crop'
  },
  {
    id: '3',
    title: 'Instagram\'s app notifications',
    isNew: true,
    likes: 40,
    category: 'Engagement',
    company: 'Instagram',
    market: 'B2C',
    objective: ['Notification', 'Engagement'],
    description: 'Analysis of Instagram\'s notification strategy to drive user engagement',
    image: 'https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?w=500&auto=format&fit=crop'
  },
  {
    id: '4',
    title: 'Building & Monetizing Sports Community On Fancode',
    isNew: true,
    likes: 33,
    category: 'Monetization',
    company: 'Fancode',
    market: 'B2C',
    objective: ['Monetization', 'Engagement'],
    description: 'How Fancode built and monetized their sports community platform',
    image: 'https://images.unsplash.com/photo-1562077772-3bd90403f7f0?w=500&auto=format&fit=crop'
  },
  {
    id: '5',
    title: 'Improving acquisition and retention of EatClub',
    isNew: true,
    likes: 55,
    category: 'Growth',
    company: 'EatClub',
    market: 'B2C',
    objective: ['Acquisition', 'Adoption'],
    description: 'Strategies employed by EatClub to acquire and retain customers',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&auto=format&fit=crop'
  },
  {
    id: '6',
    title: 'Launching Consumer Durable Financing/Personal Loans MVP for OTO',
    isNew: true,
    likes: 26,
    category: 'Product Launch',
    company: 'OTO',
    market: 'B2C & B2B',
    objective: ['MVP', 'GTM'],
    description: 'How OTO launched their financing product MVP and brought it to market',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&auto=format&fit=crop'
  },
  {
    id: '7',
    title: 'Conversational AI with ChatGPT',
    isNew: true,
    likes: 43,
    category: 'AI',
    company: 'OpenAI',
    market: 'B2B',
    objective: ['Personalization'],
    description: 'Analysis of ChatGPT\'s conversational AI capabilities',
    image: 'https://images.unsplash.com/photo-1655720828018-edd2daee8c1b?w=500&auto=format&fit=crop'
  },
  {
    id: '8',
    title: 'Personalisation & Search in Spotify',
    isNew: true,
    likes: 37,
    category: 'Personalization',
    company: 'Spotify',
    market: 'B2C',
    objective: ['Personalization', 'Engagement'],
    description: 'How Spotify personalizes user experience through search and recommendations',
    image: 'https://images.unsplash.com/photo-1611339555312-e607c8352fd7?w=500&auto=format&fit=crop'
  },
  {
    id: '9',
    title: 'Conversational AI in Bing',
    isNew: true,
    likes: 14,
    category: 'AI',
    company: 'Microsoft',
    market: 'B2C & B2B',
    objective: ['Personalization'],
    description: 'Analysis of Bing\'s implementation of conversational AI',
    image: 'https://images.unsplash.com/photo-1633419461186-7d40a38105ec?w=500&auto=format&fit=crop'
  },
  {
    id: '10',
    title: 'Reducing Ride Cancellations in Rapido',
    isNew: true,
    likes: 99,
    category: 'User Retention',
    company: 'Rapido',
    market: 'B2C',
    objective: ['Conversion', 'Engagement'],
    description: 'Strategies implemented by Rapido to reduce ride cancellations',
    image: 'https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?w=500&auto=format&fit=crop'
  },
  {
    id: '11',
    title: 'Personalisation and Search on YouTube',
    isNew: true,
    likes: 25,
    category: 'Personalization',
    company: 'YouTube',
    market: 'B2C',
    objective: ['Personalization', 'Engagement'],
    description: 'How YouTube personalizes content and improves search functionality',
    image: 'https://images.unsplash.com/photo-1570430224763-4a6f87987ce3?w=500&auto=format&fit=crop'
  },
  {
    id: '12',
    title: 'BookMyShow\'s new User Onboarding',
    isNew: true,
    likes: 11,
    category: 'Onboarding',
    company: 'BookMyShow',
    market: 'B2C',
    objective: ['Onboarding', 'First Time Experience'],
    description: 'Analysis of BookMyShow\'s user onboarding flow',
    image: 'https://images.unsplash.com/photo-1572177215652-32e68733525c?w=500&auto=format&fit=crop'
  },
  {
    id: '13',
    title: 'Kompanion\'s new User Onboarding',
    isNew: true,
    likes: 86,
    category: 'Onboarding',
    company: 'Kompanion',
    market: 'B2B',
    objective: ['Onboarding', 'First Time Experience'],
    description: 'How Kompanion redesigned their user onboarding experience',
    image: 'https://images.unsplash.com/photo-1572177215652-32e68733525c?w=500&auto=format&fit=crop'
  },
  {
    id: '14',
    title: 'Cult.fits new user onboarding',
    isNew: true,
    likes: 32,
    category: 'Onboarding',
    company: 'Cult.fit',
    market: 'B2C',
    objective: ['Onboarding', 'First Time Experience'],
    description: 'Analysis of Cult.fit\'s user onboarding process',
    image: 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=500&auto=format&fit=crop'
  },
  {
    id: '15',
    title: 'Freecharge\'s Referral Program',
    isNew: true,
    likes: 15,
    category: 'Growth',
    company: 'Freecharge',
    market: 'B2C',
    objective: ['Acquisition', 'Growth'],
    description: 'How Freecharge designed their referral program for growth',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&auto=format&fit=crop'
  }
];

// Helper function to extract unique categories from data
export const getUniqueCategories = (): string[] => {
  return [...new Set(caseStudiesData.map(study => study.category))];
};

// Helper function to extract unique companies from data
export const getUniqueCompanies = (): string[] => {
  return [...new Set(caseStudiesData.map(study => study.company))];
};

// Helper function to extract unique objectives from data
export const getUniqueObjectives = (): string[] => {
  const allObjectives = caseStudiesData.flatMap(study => study.objective);
  return [...new Set(allObjectives)];
};
