import React, { useEffect, useState, Component } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ObjectiveDebugger from "@/components/ObjectiveDebugger";
import ObjectiveRenderingTest from "@/components/ObjectiveRenderingTest";
import ComponentTestSuite from "@/components/ComponentTestSuite";
import AutoPdfTestComponent from "@/components/AutoPdfTestComponent";
import NocodbCaseStudiesList from "@/components/NocodbCaseStudiesList";
import NocodbTestComponent from "@/components/NocodbTestComponent";
import NocodbApiTester from "@/components/NocodbApiTester";
import PdfTestCaseStudies from "@/components/PdfTestCaseStudies";
import BaserowCaseStudiesList from "@/components/BaserowCaseStudiesList";
import DirectBaserowCaseStudies from "@/components/DirectBaserowCaseStudies";
import MockBaserowCaseStudies from "@/components/MockBaserowCaseStudies";
import AlternativeBaserowCaseStudies from "@/components/AlternativeBaserowCaseStudies";
import ServerProxyBaserowCaseStudies from "@/components/ServerProxyBaserowCaseStudies";

// Custom error boundary component
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-md">
          <h2 className="text-xl font-bold text-red-800 mb-2">
            Something went wrong:
          </h2>
          <pre className="text-sm bg-white p-3 rounded border border-red-100 overflow-auto mb-4">
            {this.state.error?.message || "Unknown error"}
          </pre>
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading component
const Loading = () => (
  <div className="flex justify-center items-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-stare-navy"></div>
  </div>
);

const CaseStudies = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    console.log("CaseStudies component mounted");
    setIsLoaded(true);

    // Log the NocoDB API configuration
    console.log("NocoDB API Configuration:", {
      apiUrl: "https://app.nocodb.com/api/v2",
      tableId: "mj1s93pidxngxnf",
      viewId: "vwiex09hf5rmqe6g",
      apiToken: "HrLL5CJ07sSRGCLeCqxexsj1dWhyRxqYXElVK5jD",
    });

    return () => {
      console.log("CaseStudies component unmounted");
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-1 container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8 text-center">Case Studies</h1>

        <div className="mb-8 p-4 border rounded bg-gray-50">
          <p>Debug info - Component loaded: {isLoaded ? "Yes" : "No"}</p>
        </div>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              🐛 Objective Rendering Debugger - Error Source Identification
            </h2>
            <ObjectiveDebugger />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              🔧 Objective Rendering Fix - React Error Resolution
            </h2>
            <ObjectiveRenderingTest />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Component Test Suite - All Fixes Verification
            </h2>
            <ComponentTestSuite />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Auto-Display PDF Test (No Toggle Buttons)
            </h2>
            <AutoPdfTestComponent />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              PDF Viewer Test Cases (Legacy)
            </h2>
            <PdfTestCaseStudies />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              NocoDB API Connection Test
            </h2>
            <NocodbApiTester />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              NocoDB Integration Demo (Mock Data)
            </h2>
            <NocodbTestComponent />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies from NocoDB (Live API - Requires Token)
            </h2>
            <NocodbCaseStudiesList />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies from Baserow (Legacy Implementation)
            </h2>
            <BaserowCaseStudiesList />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies from Baserow (Direct Implementation)
            </h2>
            <DirectBaserowCaseStudies />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies (Alternative API Implementation)
            </h2>
            <AlternativeBaserowCaseStudies />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies (Server Proxy Implementation)
            </h2>
            <ServerProxyBaserowCaseStudies />
          </div>
        </ErrorBoundary>

        <ErrorBoundary>
          <div className="border p-4 rounded-md">
            <h2 className="text-xl font-semibold mb-4">
              Case Studies (Mock Implementation with Baserow Token)
            </h2>
            <MockBaserowCaseStudies />
          </div>
        </ErrorBoundary>
      </main>
      <Footer />
    </div>
  );
};

export default CaseStudies;
