import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, AlertCircle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { fetchCaseStudies } from "@/services/baserowApi";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CaseStudy } from "@/types/caseStudy";
import { caseStudiesData } from "@/data/caseStudiesData";

// Modal component for PDF viewing
const PdfModal = ({ isOpen, onClose, pdfUrl, title }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-xl font-semibold">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        <div className="flex-1 min-h-0">
          {pdfUrl ? (
            <iframe
              src={pdfUrl}
              className="w-full h-full border-0"
              title={`PDF Viewer - ${title}`}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p>No PDF available for this case study.</p>
            </div>
          )}
        </div>
        <div className="p-4 border-t flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
          >
            Close
          </button>
          <button
            onClick={() => window.open(pdfUrl, "_blank")}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Open in New Tab
          </button>
        </div>
      </div>
    </div>
  );
};

// Simplified version of CaseStudiesList with Baserow API integration
const SimpleCaseStudiesList = () => {
  // State for PDF modal
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState({ url: "", title: "" });

  // Fetch case studies from Baserow API
  const {
    data: baserowCaseStudies,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["caseStudies"],
    queryFn: fetchCaseStudies,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {caseStudies.map((caseStudy) => (
        <Card
          key={caseStudy.id}
          className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
          onClick={() => alert(`Would open PDF: ${caseStudy.pdfUrl}`)}
        >
          <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
            <img
              src={caseStudy.image}
              alt={`${caseStudy.title} logo`}
              className="max-h-full max-w-full object-contain"
              onError={(e) => {
                e.currentTarget.src =
                  "https://placehold.co/600x400?text=No+Logo";
              }}
            />
          </div>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-xl font-semibold text-stare-navy">
                {caseStudy.title}
              </h3>
              {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
            </div>
            <div className="flex flex-wrap items-center gap-2 mb-3">
              {caseStudy.objective.map((objective, index) => {
                // Handle case where objective might be an object with id, value, color
                const objectiveText =
                  typeof objective === "object" &&
                  objective !== null &&
                  "value" in objective
                    ? objective.value.toString()
                    : objective.toString();

                return (
                  <Badge key={index} variant="outline">
                    {objectiveText}
                  </Badge>
                );
              })}
            </div>
            <p className="text-gray-600 mb-4">{caseStudy.description}</p>
            <div className="flex justify-between items-center">
              <div className="text-gray-500">
                <span className="font-medium">Name:</span> {caseStudy.category}
              </div>
              {caseStudy.pdfUrl && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  PDF
                </Badge>
              )}
            </div>
            <div className="text-gray-500">
              <span className="font-medium">Company:</span> {caseStudy.company}
            </div>
            {caseStudy.creator && (
              <div className="text-gray-500">
                <span className="font-medium">Creator:</span>{" "}
                {caseStudy.creator}
              </div>
            )}
            <div className="text-gray-500">
              <span className="font-medium">Market:</span> {caseStudy.market}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default SimpleCaseStudiesList;
