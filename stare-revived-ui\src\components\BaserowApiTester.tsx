import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { testBaserowConnection } from '@/utils/apiTester';
import { baserowConfig } from '@/config/api.config';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const BaserowApiTester = () => {
  const [testResult, setTestResult] = useState<{
    success?: boolean;
    message?: string;
    data?: any;
  }>({});
  const [loading, setLoading] = useState(false);
  const [apiToken, setApiToken] = useState(baserowConfig.apiToken);
  const [showToken, setShowToken] = useState(false);

  const handleTest = async () => {
    setLoading(true);
    try {
      // Update the token in the config
      baserowConfig.apiToken = apiToken;
      
      const result = await testBaserowConnection();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: 'An unexpected error occurred during testing',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Baserow API Connection Tester</CardTitle>
        <CardDescription>
          Test your connection to the Baserow API by entering your API token below
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="api-token">Baserow API Token</Label>
          <div className="flex space-x-2">
            <Input
              id="api-token"
              type={showToken ? 'text' : 'password'}
              value={apiToken}
              onChange={(e) => setApiToken(e.target.value)}
              placeholder="Enter your Baserow API token"
              className="flex-1"
            />
            <Button
              variant="outline"
              type="button"
              onClick={() => setShowToken(!showToken)}
            >
              {showToken ? 'Hide' : 'Show'}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            This token will be used to authenticate with the Baserow API
          </p>
        </div>

        {testResult.success !== undefined && (
          <Alert variant={testResult.success ? 'default' : 'destructive'}>
            {testResult.success ? (
              <CheckCircle2 className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertTitle>
              {testResult.success ? 'Connection Successful' : 'Connection Failed'}
            </AlertTitle>
            <AlertDescription>{testResult.message}</AlertDescription>
          </Alert>
        )}

        {testResult.success && testResult.data && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Sample Data:</h3>
            <pre className="bg-muted p-2 rounded-md text-xs overflow-auto max-h-40">
              {JSON.stringify(testResult.data, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleTest} disabled={loading}>
          {loading ? 'Testing...' : 'Test Connection'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default BaserowApiTester;
