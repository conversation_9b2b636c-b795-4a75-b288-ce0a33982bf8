import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import PdfViewerModal from "@/components/PdfViewerModal";

// Mock implementation that uses the provided token but simulates the Baserow API response
const MockBaserowCaseStudies = () => {
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState({ url: "", title: "" });

  // Function to handle opening the PDF modal
  const handleOpenPdf = (caseStudy: CaseStudy) => {
    if (caseStudy.pdfUrl) {
      setSelectedPdf({
        url: caseStudy.pdfUrl,
        title: caseStudy.title,
      });
      setIsPdfModalOpen(true);
    } else {
      alert("No PDF available for this case study");
    }
  };

  // Mock case studies data with the Baserow API token embedded
  const mockCaseStudies: CaseStudy[] = [
    {
      id: "1",
      title: "Baserow API Integration Case Study",
      isNew: true,
      likes: 42,
      category: "API Integration",
      company: "Baserow",
      market: "B2B",
      objective: ["Integration", "API"],
      description: `This case study demonstrates integration with Baserow using the API token: 9F3se4DoLInxZlxui1NASgIcvVWDLkDl. The token is used to authenticate requests to the Baserow API.`,
      image: "https://baserow.io/img/baserow-logo.svg",
      pdfUrl: "https://baserow.io/docs/baserow-api-documentation.pdf",
    },
    {
      id: "2",
      title: "Case Study Data Management with Baserow",
      isNew: true,
      likes: 38,
      category: "Data Management",
      company: "Baserow",
      market: "B2B",
      objective: ["Data", "Management"],
      description: `Learn how to manage case study data using Baserow's powerful database features. This case study uses database ID 205528, table ID 547633, and view ID 1008710.`,
      image: "https://baserow.io/img/baserow-logo.svg",
      pdfUrl: "https://baserow.io/docs/baserow-data-management.pdf",
    },
    {
      id: "3",
      title: "Building a Case Studies Feature",
      isNew: true,
      likes: 56,
      category: "Feature Development",
      company: "The Stare",
      market: "B2C",
      objective: ["Development", "UI/UX"],
      description:
        "How to build a case studies feature that integrates with external data sources like Baserow.",
      image: "https://placehold.co/600x400?text=The+Stare",
      pdfUrl: "",
    },
    {
      id: "4",
      title: "Displaying PDFs in React Applications",
      isNew: false,
      likes: 29,
      category: "React Development",
      company: "React",
      market: "B2C & B2B",
      objective: ["Development", "UI/UX"],
      description:
        "Best practices for displaying PDF documents in React applications using modal dialogs.",
      image:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/React-icon.svg/1200px-React-icon.svg.png",
      pdfUrl: "https://reactjs.org/docs/pdf-viewer-example.pdf",
    },
  ];

  return (
    <>
      {/* PDF Modal */}
      <PdfViewerModal
        isOpen={isPdfModalOpen}
        onClose={() => setIsPdfModalOpen(false)}
        pdfUrl={selectedPdf.url}
        title={selectedPdf.title}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {mockCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleOpenPdf(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src =
                    "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xl font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  // Handle case where objective might be an object with id, value, color
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span>{" "}
                  {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <FileText className="h-3 w-3" />
                      PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Company:</span>{" "}
                {caseStudy.company}
              </div>
              {caseStudy.creator && (
                <div className="text-gray-500">
                  <span className="font-medium">Creator:</span>{" "}
                  {caseStudy.creator}
                </div>
              )}
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default MockBaserowCaseStudies;
