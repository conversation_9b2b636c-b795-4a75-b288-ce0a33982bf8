import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp, AlertTriangle } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import PdfViewerModal from "@/components/PdfViewerModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Test component showing NocoDB integration structure
const NocodbTestComponent = () => {
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState({ url: "", title: "" });

  // Function to handle opening the PDF modal
  const handleOpenPdf = (caseStudy: CaseStudy) => {
    if (caseStudy.pdfUrl) {
      setSelectedPdf({
        url: caseStudy.pdfUrl,
        title: caseStudy.title,
      });
      setIsPdfModalOpen(true);
    } else {
      alert("No PDF available for this case study");
    }
  };

  // Mock case studies data showing NocoDB structure
  const mockNocodbCaseStudies: CaseStudy[] = [
    {
      id: "nocodb_1",
      title: "NocoDB API Integration Case Study",
      isNew: true,
      likes: 85,
      category: "API Integration",
      company: "NocoDB",
      market: "B2B",
      objective: ["Integration", "API", "Database"],
      description: `This case study demonstrates integration with NocoDB using the API endpoint: https://app.nocodb.com/api/v2/tables/mj1s93pidxngxnf/records. The integration uses xc-token authentication header.`,
      image: "https://docs.nocodb.com/assets/img/NocoDB-logo.png",
      pdfUrl: "https://docs.nocodb.com/getting-started/installation",
    },
    {
      id: "nocodb_2",
      title: "Modern Database Management with NocoDB",
      isNew: true,
      likes: 72,
      category: "Database Management",
      company: "NocoDB",
      market: "B2B",
      objective: ["Database", "Management", "API"],
      description: `Learn how to manage case study data using NocoDB's powerful API features. This case study uses table ID mj1s93pidxngxnf and view ID vwiex09hf5rmqe6g.`,
      image: "https://docs.nocodb.com/assets/img/NocoDB-logo.png",
      pdfUrl: "https://docs.nocodb.com/developer-resources/rest-apis",
    },
    {
      id: "nocodb_3",
      title: "Migrating from Baserow to NocoDB",
      isNew: true,
      likes: 94,
      category: "Migration",
      company: "The Stare",
      market: "B2C & B2B",
      objective: ["Migration", "API", "Development"],
      description: "How to successfully migrate from Baserow API to NocoDB API while maintaining data integrity and UI functionality.",
      image: "https://placehold.co/600x400?text=The+Stare",
      pdfUrl: "",
    },
    {
      id: "nocodb_4",
      title: "API Authentication Best Practices",
      isNew: false,
      likes: 67,
      category: "Security",
      company: "NocoDB",
      market: "B2C & B2B",
      objective: ["Security", "API", "Authentication"],
      description: "Best practices for securing API tokens and implementing proper authentication with NocoDB's xc-token system.",
      image: "https://docs.nocodb.com/assets/img/NocoDB-logo.png",
      pdfUrl: "https://docs.nocodb.com/account-settings/api-tokens",
    },
  ];

  return (
    <>
      {/* PDF Modal */}
      <PdfViewerModal
        isOpen={isPdfModalOpen}
        onClose={() => setIsPdfModalOpen(false)}
        pdfUrl={selectedPdf.url}
        title={selectedPdf.title}
      />

      {/* API Token Warning */}
      <Alert className="mb-6">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>API Token Required</AlertTitle>
        <AlertDescription>
          To use the NocoDB integration, you need to:
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Visit <a href="https://app.nocodb.com/#/account/tokens" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">NocoDB Account Tokens</a></li>
            <li>Generate a new API token</li>
            <li>Replace "YOUR_NOCODB_API_TOKEN_HERE" in the configuration with your actual token</li>
            <li>Update the API endpoint URL if needed</li>
          </ol>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {mockNocodbCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleOpenPdf(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src =
                    "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xl font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  // Handle case where objective might be an object with id, value, color
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span>{" "}
                  {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <FileText className="h-3 w-3" />
                      PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Company:</span>{" "}
                {caseStudy.company}
              </div>
              {caseStudy.creator && (
                <div className="text-gray-500">
                  <span className="font-medium">Creator:</span>{" "}
                  {caseStudy.creator}
                </div>
              )}
              <div className="text-gray-500">
                <span className="font-medium">Market:</span>{" "}
                {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default NocodbTestComponent;
