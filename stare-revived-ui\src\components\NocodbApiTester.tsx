import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { nocodbConfig } from "@/config/api.config";
import axios from "axios";

// Simple API tester component for NocoDB
const NocodbApiTester = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const testApiConnection = async () => {
    setIsLoading(true);
    setError(null);
    setApiResponse(null);
    setIsSuccess(false);

    try {
      console.log("Testing NocoDB API connection...");
      
      const url = `${nocodbConfig.apiUrl}/tables/${nocodbConfig.tableId}/records`;
      const params = {
        ...nocodbConfig.defaultParams,
        viewId: nocodbConfig.viewId,
      };

      console.log("Request URL:", url);
      console.log("Request params:", params);
      console.log("API Token:", nocodbConfig.apiToken.substring(0, 10) + "...");

      const response = await axios.get(url, {
        params,
        headers: {
          "Content-Type": "application/json",
          "xc-token": nocodbConfig.apiToken,
        },
        timeout: 10000,
      });

      console.log("API Response:", response.data);
      setApiResponse(response.data);
      setIsSuccess(true);
    } catch (err: any) {
      console.error("API Test Error:", err);
      
      let errorMessage = "Unknown error occurred";
      
      if (axios.isAxiosError(err)) {
        if (err.response) {
          errorMessage = `HTTP ${err.response.status}: ${err.response.statusText}`;
          if (err.response.data) {
            errorMessage += ` - ${JSON.stringify(err.response.data)}`;
          }
        } else if (err.request) {
          errorMessage = "No response received from server (network/CORS issue)";
        } else {
          errorMessage = err.message;
        }
      } else {
        errorMessage = err.message || err.toString();
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-test on component mount
  useEffect(() => {
    testApiConnection();
  }, []);

  return (
    <div className="space-y-4">
      {/* API Configuration Display */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold mb-3">NocoDB API Configuration</h3>
          <div className="space-y-2 text-sm">
            <div><strong>API URL:</strong> {nocodbConfig.apiUrl}</div>
            <div><strong>Table ID:</strong> {nocodbConfig.tableId}</div>
            <div><strong>View ID:</strong> {nocodbConfig.viewId}</div>
            <div><strong>API Token:</strong> {nocodbConfig.apiToken.substring(0, 10)}...</div>
          </div>
        </CardContent>
      </Card>

      {/* Test Button */}
      <div className="flex justify-center">
        <button
          onClick={testApiConnection}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Testing API...
            </>
          ) : (
            "Test NocoDB API Connection"
          )}
        </button>
      </div>

      {/* Success State */}
      {isSuccess && apiResponse && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">API Connection Successful!</AlertTitle>
          <AlertDescription className="text-green-700">
            <div className="mt-2">
              <strong>Response Summary:</strong>
              <ul className="list-disc list-inside mt-1">
                {apiResponse.list && (
                  <li>Found {apiResponse.list.length} records in 'list' array</li>
                )}
                {apiResponse.records && (
                  <li>Found {apiResponse.records.length} records in 'records' array</li>
                )}
                {Array.isArray(apiResponse) && (
                  <li>Found {apiResponse.length} records in direct array</li>
                )}
                <li>Response keys: {Object.keys(apiResponse).join(", ")}</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>API Connection Failed</AlertTitle>
          <AlertDescription>
            <div className="mt-2">
              <strong>Error Details:</strong>
              <p className="mt-1 text-sm font-mono bg-red-100 p-2 rounded">{error}</p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Raw Response Display */}
      {apiResponse && (
        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold mb-3">Raw API Response</h3>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-96">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default NocodbApiTester;
