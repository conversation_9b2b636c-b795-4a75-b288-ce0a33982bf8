import React, { useState, useEffect } from "react";
import {
  X,
  FileText,
  ThumbsUp,
  Building,
  Target,
  User,
  Globe,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CaseStudy } from "@/types/caseStudy";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { safePdfUrl, isValidPdfUrl } from "@/utils/objectiveUtils";

interface CaseStudyDetailModalProps {
  caseStudy: CaseStudy | null;
  isOpen: boolean;
  onClose: () => void;
}

const CaseStudyDetailModal: React.FC<CaseStudyDetailModalProps> = ({
  caseStudy,
  isOpen,
  onClose,
}) => {
  const [pdfLoading, setPdfLoading] = useState(false);
  const [pdfError, setPdfError] = useState(false);

  // Reset PDF state when modal opens/closes or case study changes
  useEffect(() => {
    if (isOpen && caseStudy && isValidPdfUrl(caseStudy.pdfUrl)) {
      setPdfLoading(true);
      setPdfError(false);
    } else {
      setPdfLoading(false);
      setPdfError(false);
    }
  }, [isOpen, caseStudy?.id, caseStudy?.pdfUrl]);

  if (!isOpen || !caseStudy) return null;

  // Check if PDF should be displayed using the utility function
  const shouldShowPdf = isValidPdfUrl(caseStudy.pdfUrl);

  // Function to handle PDF load success
  const handlePdfLoad = () => {
    setPdfLoading(false);
    setPdfError(false);
  };

  // Function to handle PDF load error
  const handlePdfError = () => {
    setPdfLoading(false);
    setPdfError(true);
  };

  // Function to format PDF URL for iframe
  const formatPdfUrl = (url: any): string => {
    const safeUrl = safePdfUrl(url);
    if (!safeUrl) return "";

    // If it's already a proper URL, return as is
    if (safeUrl.startsWith("http://") || safeUrl.startsWith("https://")) {
      // For Google Drive links, convert to embed format
      if (safeUrl.includes("drive.google.com")) {
        const fileId = safeUrl.match(/\/d\/([a-zA-Z0-9-_]+)/)?.[1];
        if (fileId) {
          return `https://drive.google.com/file/d/${fileId}/preview`;
        }
      }
      return safeUrl;
    }

    // If it's a relative path, assume it's from the same domain
    return safeUrl.startsWith("/") ? safeUrl : `/${safeUrl}`;
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <>
      {/* Main Detail Modal */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={handleBackdropClick}
        role="dialog"
        aria-modal="true"
        aria-labelledby="case-study-title"
      >
        <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex justify-between items-start p-6 border-b bg-gradient-to-r from-stare-navy to-blue-700 text-white">
            <div className="flex-1 pr-4">
              <div className="flex items-center gap-3 mb-2">
                <h1 id="case-study-title" className="text-2xl font-bold">
                  {caseStudy.title}
                </h1>
                {caseStudy.isNew && (
                  <Badge
                    variant="secondary"
                    className="bg-yellow-400 text-yellow-900"
                  >
                    New
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-blue-100">
                <div className="flex items-center gap-1">
                  <Building className="h-4 w-4" />
                  <span>{caseStudy.company}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>{caseStudy.market}</span>
                </div>
                <div className="flex items-center gap-1">
                  <ThumbsUp className="h-4 w-4" />
                  <span>{caseStudy.likes} likes</span>
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-300 transition-colors p-1"
              aria-label="Close modal"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div
              className={`${
                shouldShowPdf ? "grid grid-cols-1 lg:grid-cols-2 gap-6" : ""
              } h-full`}
            >
              {/* Case Study Details */}
              <div className="p-6 space-y-6">
                {/* Company Logo and Basic Info */}
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-32 h-32 bg-gray-50 rounded-lg flex items-center justify-center border">
                      <img
                        src={caseStudy.image}
                        alt={`${caseStudy.company} logo`}
                        className="max-w-full max-h-full object-contain"
                        onError={(e) => {
                          e.currentTarget.src =
                            "https://placehold.co/128x128?text=Logo";
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex-1 space-y-4">
                    {/* Category/Name */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        Category
                      </h3>
                      <Badge variant="outline" className="text-base px-3 py-1">
                        {caseStudy.category}
                      </Badge>
                    </div>

                    {/* Objectives */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Objectives
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {caseStudy.objective.map((objective, index) => {
                          const objectiveText =
                            typeof objective === "object" &&
                            objective !== null &&
                            "value" in objective
                              ? objective.value.toString()
                              : objective.toString();

                          return (
                            <Badge key={index} variant="secondary">
                              {objectiveText}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>

                    {/* Creator */}
                    {caseStudy.creator && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                          <User className="h-5 w-5" />
                          Creator
                        </h3>
                        <p className="text-gray-700">{caseStudy.creator}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    Description
                  </h3>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {caseStudy.description}
                    </p>
                  </div>
                </div>

                {/* Additional Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-gray-50 p-4 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      Company
                    </h4>
                    <p className="text-gray-700">{caseStudy.company}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Market</h4>
                    <p className="text-gray-700">{caseStudy.market}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      Category
                    </h4>
                    <p className="text-gray-700">{caseStudy.category}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      Engagement
                    </h4>
                    <div className="flex items-center gap-1 text-gray-700">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{caseStudy.likes} likes</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* PDF Viewer Section */}
              {shouldShowPdf && (
                <div className="flex flex-col h-full">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      PDF Document
                    </h3>
                  </div>

                  <div className="flex-1 relative bg-gray-100">
                    {pdfLoading && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
                        <div className="flex flex-col items-center gap-3">
                          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                          <p className="text-gray-600">Loading PDF...</p>
                        </div>
                      </div>
                    )}

                    {pdfError && (
                      <div className="absolute inset-0 flex items-center justify-center p-4">
                        <Alert variant="destructive" className="max-w-md">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            Failed to load PDF. The document may not be
                            available or the URL may be invalid.
                            <div className="mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  window.open(
                                    safePdfUrl(caseStudy.pdfUrl),
                                    "_blank"
                                  )
                                }
                              >
                                Open in New Tab
                              </Button>
                            </div>
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}

                    {!pdfError && (
                      <iframe
                        src={formatPdfUrl(caseStudy.pdfUrl)}
                        className="w-full h-full border-0"
                        title={`${caseStudy.title} PDF`}
                        onLoad={handlePdfLoad}
                        onError={handlePdfError}
                        style={{ minHeight: "500px" }}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer with Actions */}
          <div className="border-t bg-gray-50 p-6">
            <div className="flex flex-col sm:flex-row gap-3 justify-between items-center">
              <div className="text-sm text-gray-600">
                Case Study ID: {caseStudy.id}
                {shouldShowPdf && (
                  <span className="ml-4 text-blue-600">
                    • PDF Document Available
                  </span>
                )}
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CaseStudyDetailModal;
