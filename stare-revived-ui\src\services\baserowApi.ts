import axios from "axios";
import { CaseStudy } from "@/types/caseStudy";
import { baserowConfig } from "@/config/api.config";

// Create axios instance for Baserow API
export const baserowClient = axios.create({
  baseURL: baserowConfig.apiUrl,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Token ${baserowConfig.apiToken}`,
  },
  // Add timeout and additional options
  timeout: 10000, // 10 seconds
  validateStatus: (status) => {
    return status >= 200 && status < 300; // Default
  },
});

// Function to map Baserow data to our CaseStudy type
const mapBaserowToCaseStudy = (item: any): CaseStudy => {
  console.log("Mapping Baserow item to CaseStudy:", item);

  // Map the fields from Baserow to our CaseStudy type
  // Adjust the field names based on your Baserow table structure
  return {
    id: item.id?.toString() || Math.random().toString(36).substring(7),
    title: item.Title || item.Name || "", // Try both Title and Name fields
    isNew: item.IsNew === "true" || item.IsNew === true || false,
    likes: parseInt(item.Likes || "0") || 0, // Parse likes from Baserow
    category: item.Category || "", // This will be used as the name
    company: item.Company || "",
    creator: item.Creator || undefined,
    market: (item.Market as "B2C" | "B2B" | "B2C & B2B") || "B2C",
    objective: parseObjectives(item.Objective || ""),
    description: item.Description || "",
    image:
      item.Logo || item.Image || "https://placehold.co/600x400?text=No+Logo", // Try both Logo and Image fields
    pdfUrl: item.PDF || item.PdfUrl || "", // Try both PDF and PdfUrl fields
  };
};

// Helper function to parse objectives from string to array
const parseObjectives = (objectivesStr: any): string[] => {
  console.log("Parsing objectives:", objectivesStr);

  if (!objectivesStr) return [];

  // If it's already an array, ensure all items are strings
  if (Array.isArray(objectivesStr)) {
    return objectivesStr.map((obj) => {
      // Handle case where obj might be an object with id, value, color
      if (obj && typeof obj === "object" && "value" in obj) {
        return obj.value.toString().trim();
      }
      return obj.toString().trim();
    });
  }

  // If it's a JSON string, try to parse it
  if (
    typeof objectivesStr === "string" &&
    (objectivesStr.startsWith("[") || objectivesStr.startsWith("{"))
  ) {
    try {
      const parsed = JSON.parse(objectivesStr);
      if (Array.isArray(parsed)) {
        return parsed.map((obj) => {
          // Handle case where obj might be an object with id, value, color
          if (obj && typeof obj === "object" && "value" in obj) {
            return obj.value.toString().trim();
          }
          return obj.toString().trim();
        });
      }
    } catch (e) {
      console.warn("Failed to parse JSON objectives:", e);
    }
  }

  // If it's a comma-separated string, split it
  if (typeof objectivesStr === "string") {
    return objectivesStr.split(",").map((obj) => obj.trim());
  }

  // If objectivesStr is an object with id, value, color
  if (
    objectivesStr &&
    typeof objectivesStr === "object" &&
    "value" in objectivesStr
  ) {
    return [objectivesStr.value.toString().trim()];
  }

  // If all else fails, convert to string and return as single item array
  return [objectivesStr.toString()];
};

// Function to fetch case studies from Baserow
export const fetchCaseStudies = async (): Promise<CaseStudy[]> => {
  try {
    console.log("Fetching case studies from Baserow with config:", {
      apiUrl: baserowConfig.apiUrl,
      tableId: baserowConfig.tableId,
      viewId: baserowConfig.viewId,
      apiToken: baserowConfig.apiToken.substring(0, 5) + "...", // Only log part of the token for security
    });

    // Try with user_field_names=true
    const url = `/database/rows/table/${baserowConfig.tableId}/?user_field_names=true&view_id=${baserowConfig.viewId}`;
    console.log("Request URL:", baserowConfig.apiUrl + url);

    try {
      const response = await baserowClient.get(url);
      console.log("Baserow API response:", response.data);

      if (response.data && response.data.results) {
        const mappedData = response.data.results.map(mapBaserowToCaseStudy);
        console.log("Mapped case studies:", mappedData);
        return mappedData;
      } else if (Array.isArray(response.data)) {
        // Handle case where response is directly an array
        const mappedData = response.data.map(mapBaserowToCaseStudy);
        console.log("Mapped case studies from array response:", mappedData);
        return mappedData;
      }
    } catch (error) {
      console.warn("Error with user_field_names=true, trying without:", error);
    }

    // Try without user_field_names
    const fallbackUrl = `/database/rows/table/${baserowConfig.tableId}/?view_id=${baserowConfig.viewId}`;
    console.log("Fallback Request URL:", baserowConfig.apiUrl + fallbackUrl);

    const fallbackResponse = await baserowClient.get(fallbackUrl);
    console.log("Fallback Baserow API response:", fallbackResponse.data);

    if (fallbackResponse.data && fallbackResponse.data.results) {
      const mappedData = fallbackResponse.data.results.map(
        mapBaserowToCaseStudy
      );
      console.log("Mapped case studies from fallback:", mappedData);
      return mappedData;
    } else if (Array.isArray(fallbackResponse.data)) {
      // Handle case where response is directly an array
      const mappedData = fallbackResponse.data.map(mapBaserowToCaseStudy);
      console.log(
        "Mapped case studies from fallback array response:",
        mappedData
      );
      return mappedData;
    }

    console.warn("No results found in any Baserow response");
    return [];
  } catch (error) {
    console.error("Error fetching case studies from Baserow:", error);
    throw error;
  }
};

// Function to fetch a single case study by ID
export const fetchCaseStudyById = async (
  id: string
): Promise<CaseStudy | null> => {
  try {
    const response = await baserowClient.get(
      `/database/rows/table/${baserowConfig.tableId}/${id}/?user_field_names=true`
    );

    if (response.data) {
      return mapBaserowToCaseStudy(response.data);
    }

    return null;
  } catch (error) {
    console.error(
      `Error fetching case study with ID ${id} from Baserow:`,
      error
    );
    throw error;
  }
};

export default {
  fetchCaseStudies,
  fetchCaseStudyById,
};
