import axios from "axios";
import { CaseStudy } from "@/types/caseStudy";
import { nocodbConfig } from "@/config/api.config";

// Create axios instance for NocoDB API
export const nocodbClient = axios.create({
  baseURL: nocodbConfig.apiUrl,
  headers: {
    "Content-Type": "application/json",
    "xc-token": nocodbConfig.apiToken,
  },
  timeout: 10000, // 10 seconds
  validateStatus: (status) => {
    return status >= 200 && status < 300; // Default
  },
});

// Helper function to parse objectives from string to array
const parseObjectives = (objectivesStr: any): string[] => {
  console.log("Parsing objectives:", objectivesStr);

  if (!objectivesStr) return [];

  // If it's already an array, ensure all items are strings
  if (Array.isArray(objectivesStr)) {
    return objectivesStr.map((obj) => {
      // Handle case where obj might be an object with id, value, color
      if (obj && typeof obj === "object" && "value" in obj) {
        return obj.value.toString().trim();
      }
      return obj.toString().trim();
    });
  }

  // If it's a JSON string, try to parse it
  if (
    typeof objectivesStr === "string" &&
    (objectivesStr.startsWith("[") || objectivesStr.startsWith("{"))
  ) {
    try {
      const parsed = JSON.parse(objectivesStr);
      if (Array.isArray(parsed)) {
        return parsed.map((obj) => {
          // Handle case where obj might be an object with id, value, color
          if (obj && typeof obj === "object" && "value" in obj) {
            return obj.value.toString().trim();
          }
          return obj.toString().trim();
        });
      }
    } catch (e) {
      console.warn("Failed to parse JSON objectives:", e);
    }
  }

  // If it's a comma-separated string, split it
  if (typeof objectivesStr === "string") {
    return objectivesStr.split(",").map((obj) => obj.trim());
  }

  // If objectivesStr is an object with id, value, color
  if (
    objectivesStr &&
    typeof objectivesStr === "object" &&
    "value" in objectivesStr
  ) {
    return [objectivesStr.value.toString().trim()];
  }

  // If all else fails, convert to string and return as single item array
  return [objectivesStr.toString()];
};

// Function to map NocoDB data to our CaseStudy type
const mapNocodbToCaseStudy = (item: any): CaseStudy => {
  console.log("Mapping NocoDB item to CaseStudy:", item);

  // NocoDB typically returns data with field names as keys
  // Adjust the field names based on your NocoDB table structure
  return {
    id: item.Id?.toString() || item.id?.toString() || Math.random().toString(36).substring(7),
    title: item.Title || item.Name || "", // Try both Title and Name fields
    isNew: item.IsNew === "true" || item.IsNew === true || item.is_new === true || false,
    likes: parseInt(item.Likes || item.likes || "0") || 0, // Parse likes from NocoDB
    category: item.Category || item.category || "", // This will be used as the name
    company: item.Company || item.company || "",
    creator: item.Creator || item.creator || undefined,
    market: (item.Market || item.market || "B2C") as "B2C" | "B2B" | "B2C & B2B",
    objective: parseObjectives(item.Objective || item.objective || item.objectives || ""),
    description: item.Description || item.description || "",
    image: item.Logo || item.logo || item.Image || item.image || "https://placehold.co/600x400?text=No+Logo", // Try multiple field names
    pdfUrl: item.PDF || item.pdf || item.PdfUrl || item.pdf_url || "", // Try multiple field names for PDF
  };
};

// Function to fetch case studies from NocoDB
export const fetchCaseStudies = async (): Promise<CaseStudy[]> => {
  try {
    console.log("Fetching case studies from NocoDB with config:", {
      apiUrl: nocodbConfig.apiUrl,
      tableId: nocodbConfig.tableId,
      viewId: nocodbConfig.viewId,
      apiToken: nocodbConfig.apiToken.substring(0, 5) + "...", // Only log part of the token for security
    });

    // Construct the URL with query parameters
    const url = `/tables/${nocodbConfig.tableId}/records`;
    const params = {
      ...nocodbConfig.defaultParams,
      viewId: nocodbConfig.viewId,
    };

    console.log("Request URL:", nocodbConfig.apiUrl + url);
    console.log("Request params:", params);

    const response = await nocodbClient.get(url, { params });

    console.log("NocoDB API response:", response.data);

    // NocoDB typically returns data in a 'list' or 'records' array
    let records = [];
    if (response.data && response.data.list) {
      records = response.data.list;
    } else if (response.data && response.data.records) {
      records = response.data.records;
    } else if (Array.isArray(response.data)) {
      records = response.data;
    } else {
      console.warn("Unexpected NocoDB response format:", response.data);
      return [];
    }

    const mappedData = records.map(mapNocodbToCaseStudy);
    console.log("Mapped case studies:", mappedData);
    return mappedData;
  } catch (error) {
    console.error("Error fetching case studies from NocoDB:", error);
    
    // Enhanced error logging
    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.error("Response error data:", error.response.data);
        console.error("Response error status:", error.response.status);
        console.error("Response error headers:", error.response.headers);
      } else if (error.request) {
        console.error("Request error:", error.request);
      }
    }
    
    throw error;
  }
};

// Function to fetch a single case study by ID from NocoDB
export const fetchCaseStudyById = async (
  id: string
): Promise<CaseStudy | null> => {
  try {
    const response = await nocodbClient.get(
      `/tables/${nocodbConfig.tableId}/records/${id}`
    );

    if (response.data) {
      return mapNocodbToCaseStudy(response.data);
    }

    return null;
  } catch (error) {
    console.error(
      `Error fetching case study with ID ${id} from NocoDB:`,
      error
    );
    throw error;
  }
};

export default {
  fetchCaseStudies,
  fetchCaseStudyById,
};
