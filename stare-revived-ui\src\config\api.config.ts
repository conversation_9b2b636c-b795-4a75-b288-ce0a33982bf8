// API Configuration

// NocoDB API configuration
export const nocodbConfig = {
  apiUrl: "https://app.nocodb.com/api/v2",
  tableId: "mj1s93pidxngxnf",
  viewId: "vwiex09hf5rmqe6g",

  // API token for NocoDB authentication
  // In a production environment, this should come from environment variables
  apiToken: "HrLL5CJ07sSRGCLeCqxexsj1dWhyRxqYXElVK5jD",

  // Default query parameters
  defaultParams: {
    offset: 0,
    limit: 25,
    where: "",
  },
};

// Legacy Baserow configuration (kept for reference)
export const baserowConfig = {
  apiUrl: "https://api.baserow.io/api",
  databaseId: "205528",
  tableId: "547633",
  viewId: "1008710",
  apiToken: "9F3se4DoLInxZlxui1NASgIcvVWDLkDl",
};

// You can add other API configurations here as needed
