import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertTriangle, FileText, ThumbsUp } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { safePdfUrl, isValidPdfUrl } from "@/utils/objectiveUtils";

// Test component to verify PDF URL handling fixes
const PdfUrlFixTest = () => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null);

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Test case studies with different PDF URL formats that might cause issues
  const testCaseStudies: CaseStudy[] = [
    {
      id: "pdf_test_1",
      title: "Valid String PDF URL Test",
      isNew: true,
      likes: 100,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2B",
      objective: ["PDF", "String", "Testing"],
      description: "This case study has a valid string PDF URL. Should work without any trim() errors.",
      image: "https://placehold.co/400x200?text=Valid+PDF",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
    {
      id: "pdf_test_2",
      title: "Null PDF URL Test",
      isNew: true,
      likes: 95,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2B",
      objective: ["PDF", "Null", "Testing"],
      description: "This case study has a null PDF URL. Should not cause trim() errors and should show single column layout.",
      image: "https://placehold.co/400x200?text=Null+PDF",
      pdfUrl: null as any, // Simulating null from API
    },
    {
      id: "pdf_test_3",
      title: "Undefined PDF URL Test",
      isNew: false,
      likes: 85,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["PDF", "Undefined", "Testing"],
      description: "This case study has an undefined PDF URL. Should not cause trim() errors and should show single column layout.",
      image: "https://placehold.co/400x200?text=Undefined+PDF",
      pdfUrl: undefined as any, // Simulating undefined from API
    },
    {
      id: "pdf_test_4",
      title: "Empty String PDF URL Test",
      isNew: false,
      likes: 75,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["PDF", "Empty", "Testing"],
      description: "This case study has an empty string PDF URL. Should show single column layout.",
      image: "https://placehold.co/400x200?text=Empty+PDF",
      pdfUrl: "",
    },
    {
      id: "pdf_test_5",
      title: "Number PDF URL Test",
      isNew: false,
      likes: 65,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["PDF", "Number", "Testing"],
      description: "This case study has a number as PDF URL (simulating API data type issues). Should be converted to string safely.",
      image: "https://placehold.co/400x200?text=Number+PDF",
      pdfUrl: 12345 as any, // Simulating wrong data type from API
    },
    {
      id: "pdf_test_6",
      title: "Object PDF URL Test",
      isNew: false,
      likes: 55,
      category: "PDF URL Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["PDF", "Object", "Testing"],
      description: "This case study has an object as PDF URL (simulating API data issues). Should be handled safely without errors.",
      image: "https://placehold.co/400x200?text=Object+PDF",
      pdfUrl: { url: "https://example.com/test.pdf" } as any, // Simulating object from API
    },
  ];

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {/* Test Status */}
      <Alert className="mb-6">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>PDF URL Fix Verification</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>This test verifies the fix for the error: "caseStudy.pdfUrl.trim is not a function"</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>Valid String URLs:</strong> Should work normally with PDF display</li>
              <li><strong>Null/Undefined URLs:</strong> Should not cause errors, show single column</li>
              <li><strong>Empty String URLs:</strong> Should show single column layout</li>
              <li><strong>Wrong Data Types:</strong> Should be converted safely to strings</li>
              <li><strong>Expected Result:</strong> No "trim is not a function" errors</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Fix Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">CaseStudyDetailModal</h4>
                <p className="text-sm text-green-700">Fixed PDF URL handling</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">API Services</h4>
                <p className="text-sm text-green-700">Added string conversion</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">Utility Functions</h4>
                <p className="text-sm text-green-700">Created safePdfUrl helpers</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Cases */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCaseStudies.map((caseStudy) => {
          const safeUrl = safePdfUrl(caseStudy.pdfUrl);
          const isValid = isValidPdfUrl(caseStudy.pdfUrl);
          
          return (
            <Card
              key={caseStudy.id}
              className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleCaseStudyClick(caseStudy)}
            >
              <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
                <img
                  src={caseStudy.image}
                  alt={`${caseStudy.title} logo`}
                  className="max-h-full max-w-full object-contain"
                  onError={(e) => {
                    e.currentTarget.src = "https://placehold.co/600x400?text=No+Logo";
                  }}
                />
              </div>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-stare-navy">
                    {caseStudy.title}
                  </h3>
                  {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
                </div>
                <div className="flex flex-wrap items-center gap-2 mb-3">
                  {caseStudy.objective.map((objective, index) => (
                    <Badge key={`${objective}-${index}`} variant="outline">
                      {objective}
                    </Badge>
                  ))}
                </div>
                <p className="text-gray-600 mb-4 text-sm">{caseStudy.description}</p>
                
                {/* PDF URL Debug Info */}
                <div className="mb-4 p-3 bg-gray-50 rounded text-xs">
                  <div className="space-y-1">
                    <div><strong>Original:</strong> {JSON.stringify(caseStudy.pdfUrl)}</div>
                    <div><strong>Type:</strong> {typeof caseStudy.pdfUrl}</div>
                    <div><strong>Safe URL:</strong> "{safeUrl}"</div>
                    <div><strong>Is Valid:</strong> {isValid ? "✅ Yes" : "❌ No"}</div>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-gray-500">
                    <span className="font-medium">Name:</span> {caseStudy.category}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <ThumbsUp className="h-3 w-3" />
                      {caseStudy.likes}
                    </Badge>
                    {isValid ? (
                      <Badge variant="default" className="flex items-center gap-1 bg-green-600">
                        <FileText className="h-3 w-3" />
                        Valid PDF
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <AlertTriangle className="h-3 w-3" />
                        No PDF
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-gray-500 mt-2">
                  <span className="font-medium">Company:</span> {caseStudy.company}
                </div>
                <div className="text-gray-500">
                  <span className="font-medium">Market:</span> {caseStudy.market}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Debug Information */}
      <Card className="mt-6 border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Debug Information</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Fixed Error:</strong> "caseStudy.pdfUrl.trim is not a function"</p>
            <p><strong>Root Cause:</strong> pdfUrl could be null, undefined, or non-string types from API</p>
            <p><strong>Solution:</strong> Added safePdfUrl() and isValidPdfUrl() utility functions</p>
            <p><strong>Components Updated:</strong> CaseStudyDetailModal, nocodbApi, baserowApi, DirectBaserowCaseStudies</p>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default PdfUrlFixTest;
