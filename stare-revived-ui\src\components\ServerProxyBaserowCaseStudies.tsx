import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import PdfViewerModal from "@/components/PdfViewerModal";

// Server-side proxy approach (simulated)
// In a real implementation, this would use a server-side API endpoint to proxy the request to Baserow
const ServerProxyBaserowCaseStudies = () => {
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState({ url: "", title: "" });

  // Function to handle opening the PDF modal
  const handleOpenPdf = (caseStudy: CaseStudy) => {
    if (caseStudy.pdfUrl) {
      setSelectedPdf({
        url: caseStudy.pdfUrl,
        title: caseStudy.title,
      });
      setIsPdfModalOpen(true);
    } else {
      alert("No PDF available for this case study");
    }
  };

  // Simulated data that would come from a server-side proxy
  // In a real implementation, this would be fetched from a server-side API endpoint
  const serverProxyCaseStudies: CaseStudy[] = [
    {
      id: "1001",
      title: "Baserow Integration via Server-Side Proxy",
      isNew: true,
      likes: 78,
      category: "API Integration",
      company: "Baserow",
      market: "B2B",
      objective: ["Integration", "API"],
      description: `This case study demonstrates how to integrate with Baserow using a server-side proxy to avoid CORS issues. The server-side proxy would use the API token (9F3se4DoLInxZlxui1NASgIcvVWDLkDl) to authenticate with Baserow.`,
      image: "https://baserow.io/img/baserow-logo.svg",
      pdfUrl: "https://baserow.io/docs/baserow-api-documentation.pdf",
    },
    {
      id: "1002",
      title: "Secure API Token Management",
      isNew: true,
      likes: 65,
      category: "Security",
      company: "Baserow",
      market: "B2B",
      objective: ["Security", "API"],
      description: `Learn how to securely manage API tokens in your application. This case study explains why API tokens should be kept on the server side and not exposed to the client.`,
      image: "https://baserow.io/img/baserow-logo.svg",
      pdfUrl: "https://baserow.io/docs/baserow-security.pdf",
    },
    {
      id: "1003",
      title: "CORS Issues and Solutions",
      isNew: true,
      likes: 92,
      category: "Web Development",
      company: "The Stare",
      market: "B2C & B2B",
      objective: ["Development", "API"],
      description:
        "How to handle CORS issues when integrating with third-party APIs like Baserow.",
      image: "https://placehold.co/600x400?text=The+Stare",
      pdfUrl: "",
    },
    {
      id: "1004",
      title: "Building a Robust API Client",
      isNew: false,
      likes: 54,
      category: "API Development",
      company: "React",
      market: "B2C & B2B",
      objective: ["Development", "API"],
      description:
        "Best practices for building a robust API client in React applications.",
      image:
        "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/React-icon.svg/1200px-React-icon.svg.png",
      pdfUrl: "https://reactjs.org/docs/api-client-example.pdf",
    },
  ];

  return (
    <>
      {/* PDF Modal */}
      <PdfViewerModal
        isOpen={isPdfModalOpen}
        onClose={() => setIsPdfModalOpen(false)}
        pdfUrl={selectedPdf.url}
        title={selectedPdf.title}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serverProxyCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleOpenPdf(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src =
                    "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xl font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  // Handle case where objective might be an object with id, value, color
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span>{" "}
                  {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <FileText className="h-3 w-3" />
                      PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Company:</span>{" "}
                {caseStudy.company}
              </div>
              {caseStudy.creator && (
                <div className="text-gray-500">
                  <span className="font-medium">Creator:</span>{" "}
                  {caseStudy.creator}
                </div>
              )}
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default ServerProxyBaserowCaseStudies;
