import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, ThumbsUp } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";

// Test component with various PDF scenarios
const PdfTestCaseStudies = () => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null);

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Test case studies with different PDF scenarios
  const testCaseStudies: CaseStudy[] = [
    {
      id: "pdf_test_1",
      title: "Valid PDF Test - W3C Dummy PDF",
      isNew: true,
      likes: 95,
      category: "PDF Testing",
      company: "W3C",
      market: "B2B",
      objective: ["Testing", "PDF", "Validation"],
      description: "This case study tests PDF viewing with a valid W3C dummy PDF document. This should load successfully in the embedded viewer.",
      image: "https://www.w3.org/Icons/w3c_home",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
    {
      id: "pdf_test_2",
      title: "Mozilla PDF.js Test Document",
      isNew: true,
      likes: 87,
      category: "PDF Testing",
      company: "Mozilla",
      market: "B2B",
      objective: ["Testing", "PDF", "JavaScript"],
      description: "This case study uses Mozilla's PDF.js test document - a research paper about TraceMonkey. This tests PDF viewing with a more complex document.",
      image: "https://www.mozilla.org/media/img/logos/firefox/logo-quantum.9c5e96634f92.png",
      pdfUrl: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    },
    {
      id: "pdf_test_3",
      title: "Adobe Sample PDF Document",
      isNew: false,
      likes: 73,
      category: "PDF Testing",
      company: "Adobe",
      market: "B2C & B2B",
      objective: ["Testing", "PDF", "Adobe"],
      description: "This case study tests with an Adobe sample PDF document to ensure compatibility with different PDF formats and creators.",
      image: "https://www.adobe.com/content/dam/cc/icons/Adobe_Corporate_Horizontal_Red_HEX.svg",
      pdfUrl: "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf",
    },
    {
      id: "pdf_test_4",
      title: "Invalid PDF URL Test",
      isNew: false,
      likes: 12,
      category: "Error Testing",
      company: "Test Corp",
      market: "B2B",
      objective: ["Testing", "Error Handling"],
      description: "This case study tests error handling when a PDF URL is invalid or the document cannot be loaded. Should show appropriate error message.",
      image: "https://placehold.co/600x400?text=Test+Corp",
      pdfUrl: "https://invalid-url-that-does-not-exist.com/nonexistent.pdf",
    },
    {
      id: "pdf_test_5",
      title: "No PDF Available Test",
      isNew: false,
      likes: 8,
      category: "No PDF Testing",
      company: "Test Corp",
      market: "B2C",
      objective: ["Testing", "No PDF"],
      description: "This case study tests the behavior when no PDF URL is provided. The PDF button should be disabled.",
      image: "https://placehold.co/600x400?text=No+PDF",
      pdfUrl: "",
    },
    {
      id: "pdf_test_6",
      title: "Google Drive PDF Test (if accessible)",
      isNew: true,
      likes: 45,
      category: "Cloud PDF Testing",
      company: "Google",
      market: "B2C & B2B",
      objective: ["Testing", "Cloud Storage", "Google Drive"],
      description: "This case study tests PDF viewing from Google Drive. Note: This may not work due to CORS restrictions, but demonstrates the URL formatting logic.",
      image: "https://developers.google.com/drive/images/drive_icon.png",
      pdfUrl: "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
    },
  ];

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">PDF Viewer Test Cases</h3>
        <p className="text-blue-800 text-sm">
          Click on any case study card to test the embedded PDF viewer functionality. 
          Each case study tests different PDF scenarios including valid PDFs, invalid URLs, and error handling.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleCaseStudyClick(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src = "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xl font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective) => {
                  const objectiveText =
                    typeof objective === "object" &&
                    objective !== null &&
                    "value" in objective
                      ? objective.value.toString()
                      : objective.toString();

                  return (
                    <Badge key={objectiveText} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4 text-sm">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span> {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <FileText className="h-3 w-3" />
                      PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500 mt-2">
                <span className="font-medium">Company:</span> {caseStudy.company}
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};

export default PdfTestCaseStudies;
