import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertTriangle, FileText, ThumbsUp } from "lucide-react";
import { CaseStudy } from "@/types/caseStudy";
import CaseStudyDetailModal from "@/components/CaseStudyDetailModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getObjectiveText } from "@/utils/objectiveUtils";

// Test component to verify objective rendering fixes
const ObjectiveRenderingTest = () => {
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null);

  // Function to handle opening the case study detail modal
  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy);
    setIsDetailModalOpen(true);
  };

  // Function to close the detail modal
  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCaseStudy(null);
  };

  // Test case studies with different objective formats
  const testCaseStudies: CaseStudy[] = [
    {
      id: "obj_test_1",
      title: "String Objectives Test",
      isNew: true,
      likes: 100,
      category: "String Testing",
      company: "Test Corp",
      market: "B2B",
      objective: ["Testing", "String Objectives", "React Rendering"],
      description: "This case study tests rendering of simple string objectives. Should display without any errors.",
      image: "https://placehold.co/400x200?text=String+Test",
      pdfUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    },
    {
      id: "obj_test_2",
      title: "Mixed Objectives Test",
      isNew: true,
      likes: 95,
      category: "Mixed Testing",
      company: "Test Corp",
      market: "B2B",
      // Simulating mixed format that might come from API
      objective: [
        "Simple String",
        { id: 1, value: "Object with ID", color: "#blue" } as any,
        "Another String",
        { value: "Object without ID" } as any,
      ],
      description: "This case study tests rendering of mixed objective formats (strings and objects). Should handle both correctly.",
      image: "https://placehold.co/400x200?text=Mixed+Test",
      pdfUrl: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
    },
    {
      id: "obj_test_3",
      title: "Object Objectives Test",
      isNew: false,
      likes: 85,
      category: "Object Testing",
      company: "Test Corp",
      market: "B2C",
      // Simulating object format that might come from API
      objective: [
        { id: 1, value: "API Integration", color: "#green" } as any,
        { id: 2, value: "Data Management", color: "#blue" } as any,
        { id: 3, value: "User Experience", color: "#purple" } as any,
      ],
      description: "This case study tests rendering of object objectives with {id, value, color} structure. Should extract and display the value property.",
      image: "https://placehold.co/400x200?text=Object+Test",
      pdfUrl: "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf",
    },
  ];

  return (
    <>
      {/* Case Study Detail Modal */}
      <CaseStudyDetailModal
        caseStudy={selectedCaseStudy}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
      />

      {/* Test Status */}
      <Alert className="mb-6">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Objective Rendering Fix Verification</AlertTitle>
        <AlertDescription>
          <div className="space-y-2">
            <p>This test verifies the fix for the React error: "Objects are not valid as a React child"</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>String Objectives:</strong> Simple string array objectives</li>
              <li><strong>Mixed Objectives:</strong> Combination of strings and objects</li>
              <li><strong>Object Objectives:</strong> Objects with {`{id, value, color}`} structure</li>
              <li><strong>Expected Result:</strong> All objectives should display as text badges without errors</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Fix Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">SimpleCaseStudiesList</h4>
                <p className="text-sm text-green-700">Fixed objective rendering</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <h4 className="font-semibold text-green-900">CaseStudiesList</h4>
                <p className="text-sm text-green-700">Fixed objective rendering</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Cases */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {testCaseStudies.map((caseStudy) => (
          <Card
            key={caseStudy.id}
            className="bg-white rounded-md shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleCaseStudyClick(caseStudy)}
          >
            <div className="h-48 flex items-center justify-center bg-gray-50 p-4">
              <img
                src={caseStudy.image}
                alt={`${caseStudy.title} logo`}
                className="max-h-full max-w-full object-contain"
                onError={(e) => {
                  e.currentTarget.src = "https://placehold.co/600x400?text=No+Logo";
                }}
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-stare-navy">
                  {caseStudy.title}
                </h3>
                {caseStudy.isNew && <Badge variant="secondary">New</Badge>}
              </div>
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {caseStudy.objective.map((objective, index) => {
                  // Using the utility function for consistent handling
                  const objectiveText = getObjectiveText(objective as any);

                  return (
                    <Badge key={`${objectiveText}-${index}`} variant="outline">
                      {objectiveText}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-gray-600 mb-4 text-sm">{caseStudy.description}</p>
              <div className="flex justify-between items-center">
                <div className="text-gray-500">
                  <span className="font-medium">Name:</span> {caseStudy.category}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {caseStudy.likes}
                  </Badge>
                  {caseStudy.pdfUrl && (
                    <Badge variant="default" className="flex items-center gap-1 bg-blue-600">
                      <FileText className="h-3 w-3" />
                      PDF
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-gray-500 mt-2">
                <span className="font-medium">Company:</span> {caseStudy.company}
              </div>
              <div className="text-gray-500">
                <span className="font-medium">Market:</span> {caseStudy.market}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Debug Information */}
      <Card className="mt-6 border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Debug Information</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Fixed Components:</strong> SimpleCaseStudiesList.tsx, CaseStudiesList.tsx</p>
            <p><strong>Root Cause:</strong> Direct rendering of objects with {`{id, value, color}`} structure in JSX</p>
            <p><strong>Solution:</strong> Extract value property before rendering in Badge components</p>
            <p><strong>Utility:</strong> Created objectiveUtils.ts for consistent handling</p>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default ObjectiveRenderingTest;
