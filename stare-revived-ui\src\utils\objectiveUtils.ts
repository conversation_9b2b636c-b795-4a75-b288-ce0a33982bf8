/**
 * Utility functions for handling case study objectives
 * Handles both string objectives and object objectives with {id, value, color} structure
 */

export type ObjectiveValue = string | { id?: string | number; value: string; color?: string };

/**
 * Extracts the displayable text from an objective value
 * Handles both string objectives and object objectives with {id, value, color} structure
 * 
 * @param objective - The objective value (string or object)
 * @returns The displayable text as a string
 */
export const getObjectiveText = (objective: ObjectiveValue): string => {
  // Handle case where objective might be an object with id, value, color
  if (
    typeof objective === "object" &&
    objective !== null &&
    "value" in objective
  ) {
    return objective.value.toString().trim();
  }
  
  // Handle string objectives
  return objective.toString().trim();
};

/**
 * Extracts displayable text from an array of objectives
 * 
 * @param objectives - Array of objective values
 * @returns Array of displayable text strings
 */
export const getObjectiveTexts = (objectives: ObjectiveValue[]): string[] => {
  return objectives.map(getObjectiveText);
};

/**
 * Generates a unique key for an objective (for React key prop)
 * Uses the objective text and index to ensure uniqueness
 * 
 * @param objective - The objective value
 * @param index - The index in the array
 * @returns A unique key string
 */
export const getObjectiveKey = (objective: ObjectiveValue, index: number): string => {
  const text = getObjectiveText(objective);
  return `${text}-${index}`;
};

/**
 * Parses objectives from various input formats
 * Handles strings, arrays, and objects from API responses
 * 
 * @param objectivesInput - The raw objectives input from API
 * @returns Array of objective strings
 */
export const parseObjectives = (objectivesInput: any): string[] => {
  if (!objectivesInput) return [];

  // If it's already an array, process each item
  if (Array.isArray(objectivesInput)) {
    return objectivesInput.map((obj) => getObjectiveText(obj));
  }

  // If it's a JSON string, try to parse it
  if (
    typeof objectivesInput === "string" &&
    (objectivesInput.startsWith("[") || objectivesInput.startsWith("{"))
  ) {
    try {
      const parsed = JSON.parse(objectivesInput);
      if (Array.isArray(parsed)) {
        return parsed.map((obj) => getObjectiveText(obj));
      }
    } catch (e) {
      console.warn("Failed to parse JSON objectives:", e);
    }
  }

  // If it's a comma-separated string, split it
  if (typeof objectivesInput === "string") {
    return objectivesInput.split(",").map((obj) => obj.trim());
  }

  // If it's a single object with id, value, color
  if (
    objectivesInput &&
    typeof objectivesInput === "object" &&
    "value" in objectivesInput
  ) {
    return [getObjectiveText(objectivesInput)];
  }

  // If all else fails, convert to string and return as single item array
  return [objectivesInput.toString().trim()];
};
